{"name": "BushraApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/geolocation": "^3.0.6", "@react-native-picker/picker": "^2.4.10", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "react": "18.2.0", "react-native": "0.72.4", "react-native-date-picker": "^4.3.3", "react-native-document-picker": "^9.0.1", "react-native-gesture-handler": "^2.12.1", "react-native-image-picker": "^5.6.0", "react-native-maps": "^1.7.1", "react-native-paper": "^5.10.4", "react-native-permissions": "^3.8.4", "react-native-reanimated": "^3.4.2", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.24.0", "react-native-svg": "^13.14.0", "react-native-vector-icons": "^10.0.0", "react-native-view-shot": "^3.8.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.9", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.7", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}