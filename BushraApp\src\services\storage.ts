import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AppSettings, MaterialDonation, MonetaryDonation } from '@/types';
import { DEFAULT_SETTINGS } from '@/utils/constants';

// Storage Keys
const STORAGE_KEYS = {
  USER: 'user',
  SETTINGS: 'settings',
  DONATIONS: 'donations',
  IS_FIRST_LAUNCH: 'isFirstLaunch',
  THEME: 'theme',
} as const;

// User Storage
export const userStorage = {
  async saveUser(user: User): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  },

  async getUser(): Promise<User | null> {
    const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER);
    return userData ? JSON.parse(userData) : null;
  },

  async removeUser(): Promise<void> {
    await AsyncStorage.removeItem(STORAGE_KEYS.USER);
  },

  async isLoggedIn(): Promise<boolean> {
    const user = await this.getUser();
    return user !== null;
  },
};

// Settings Storage
export const settingsStorage = {
  async saveSettings(settings: AppSettings): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
  },

  async getSettings(): Promise<AppSettings> {
    const settingsData = await AsyncStorage.getItem(STORAGE_KEYS.SETTINGS);
    return settingsData ? JSON.parse(settingsData) : DEFAULT_SETTINGS;
  },

  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    const currentSettings = await this.getSettings();
    const newSettings = { ...currentSettings, ...updates };
    await this.saveSettings(newSettings);
  },
};

// Donations Storage
export const donationsStorage = {
  async saveDonation(donation: MaterialDonation | MonetaryDonation): Promise<void> {
    const donations = await this.getDonations();
    donations.push(donation);
    await AsyncStorage.setItem(STORAGE_KEYS.DONATIONS, JSON.stringify(donations));
  },

  async getDonations(): Promise<(MaterialDonation | MonetaryDonation)[]> {
    const donationsData = await AsyncStorage.getItem(STORAGE_KEYS.DONATIONS);
    return donationsData ? JSON.parse(donationsData) : [];
  },

  async updateDonationStatus(
    donationId: string,
    status: 'pending' | 'approved' | 'collected' | 'rejected'
  ): Promise<void> {
    const donations = await this.getDonations();
    const updatedDonations = donations.map(donation =>
      donation.id === donationId ? { ...donation, status, updatedAt: new Date() } : donation
    );
    await AsyncStorage.setItem(STORAGE_KEYS.DONATIONS, JSON.stringify(updatedDonations));
  },

  async getDonationById(id: string): Promise<MaterialDonation | MonetaryDonation | null> {
    const donations = await this.getDonations();
    return donations.find(donation => donation.id === id) || null;
  },
};

// Theme Storage
export const themeStorage = {
  async saveTheme(isDark: boolean): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(isDark));
  },

  async getTheme(): Promise<boolean> {
    const themeData = await AsyncStorage.getItem(STORAGE_KEYS.THEME);
    return themeData ? JSON.parse(themeData) : false;
  },
};

// First Launch Storage
export const firstLaunchStorage = {
  async setFirstLaunchComplete(): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.IS_FIRST_LAUNCH, 'false');
  },

  async isFirstLaunch(): Promise<boolean> {
    const isFirstLaunch = await AsyncStorage.getItem(STORAGE_KEYS.IS_FIRST_LAUNCH);
    return isFirstLaunch !== 'false';
  },
};
