import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { StatusBar } from 'react-native';

// Screens
import LoginScreen from '@/screens/auth/LoginScreen';
import RegisterScreen from '@/screens/auth/RegisterScreen';
import HomeScreen from '@/screens/main/HomeScreen';
import DonationFormScreen from '@/screens/donation/DonationFormScreen';
import MonetaryDonationScreen from '@/screens/donation/MonetaryDonationScreen';
import DonationConfirmationScreen from '@/screens/donation/DonationConfirmationScreen';
import AdminDashboardScreen from '@/screens/admin/AdminDashboardScreen';
import ManageDonationsScreen from '@/screens/admin/ManageDonationsScreen';

// Components
import CustomDrawerContent from '@/components/drawer/CustomDrawerContent';

// Services
import { userStorage, settingsStorage } from '@/services/storage';
import { COLORS } from '@/utils/constants';
import { RootStackParamList, User } from '@/types';

const Stack = createStackNavigator<RootStackParamList>();
const Drawer = createDrawerNavigator();

const AuthStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: COLORS.primary },
      headerTintColor: COLORS.surface,
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <Stack.Screen 
      name="Login" 
      component={LoginScreen} 
      options={{ title: 'تسجيل الدخول' }}
    />
    <Stack.Screen 
      name="Register" 
      component={RegisterScreen} 
      options={{ title: 'إنشاء حساب جديد' }}
    />
  </Stack.Navigator>
);

const MainDrawer = () => (
  <Drawer.Navigator
    drawerContent={(props) => <CustomDrawerContent {...props} />}
    screenOptions={{
      headerStyle: { backgroundColor: COLORS.primary },
      headerTintColor: COLORS.surface,
      headerTitleStyle: { fontWeight: 'bold' },
      drawerPosition: 'right',
    }}
  >
    <Drawer.Screen 
      name="Home" 
      component={HomeScreen} 
      options={{ title: 'الصفحة الرئيسية' }}
    />
  </Drawer.Navigator>
);

const AdminStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: { backgroundColor: COLORS.primary },
      headerTintColor: COLORS.surface,
      headerTitleStyle: { fontWeight: 'bold' },
    }}
  >
    <Stack.Screen 
      name="AdminDashboard" 
      component={AdminDashboardScreen} 
      options={{ title: 'لوحة التحكم الإدارية' }}
    />
    <Stack.Screen 
      name="ManageDonations" 
      component={ManageDonationsScreen} 
      options={{ title: 'إدارة التبرعات' }}
    />
  </Stack.Navigator>
);

const AppNavigator: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Load user data
      const userData = await userStorage.getUser();
      setUser(userData);

      // Load theme settings
      const settings = await settingsStorage.getSettings();
      setIsDarkMode(settings.isDarkMode);

      // Initialize daily content if needed
      await initializeDailyContent();
    } catch (error) {
      console.error('Error initializing app:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const initializeDailyContent = async () => {
    try {
      const today = new Date().toDateString();
      const lastUpdate = await settingsStorage.getLastContentUpdate();
      
      if (lastUpdate !== today) {
        // Update daily Islamic content
        const dailyContent = getDailyIslamicContent();
        await settingsStorage.updateDailyContent(dailyContent);
        await settingsStorage.setLastContentUpdate(today);
      }
    } catch (error) {
      console.error('Error updating daily content:', error);
    }
  };

  const getDailyIslamicContent = () => {
    const verses = [
      'وَمَا تُنفِقُوا مِنْ خَيْرٍ فَلِأَنفُسِكُمْ ۚ وَمَا تُنفِقُونَ إِلَّا ابْتِغَاءَ وَجْهِ اللَّهِ',
      'مَّن ذَا الَّذِي يُقْرِضُ اللَّهَ قَرْضًا حَسَنًا فَيُضَاعِفَهُ لَهُ أَضْعَافًا كَثِيرَةً',
      'وَأَقِيمُوا الصَّلَاةَ وَآتُوا الزَّكَاةَ ۚ وَمَا تُقَدِّمُوا لِأَنفُسِكُم مِّنْ خَيْرٍ تَجِدُوهُ عِندَ اللَّهِ',
    ];

    const hadiths = [
      'قال رسول الله ﷺ: "الصدقة تطفئ الخطيئة كما يطفئ الماء النار"',
      'قال رسول الله ﷺ: "من نفس عن مؤمن كربة من كرب الدنيا نفس الله عنه كربة من كرب يوم القيامة"',
      'قال رسول الله ﷺ: "أنا وكافل اليتيم في الجنة هكذا" وأشار بالسبابة والوسطى',
    ];

    const dayOfYear = Math.floor((Date.now() - new Date(new Date().getFullYear(), 0, 0).getTime()) / 86400000);
    
    return {
      verse: verses[dayOfYear % verses.length],
      hadith: hadiths[dayOfYear % hadiths.length],
    };
  };

  if (isLoading) {
    return null; // You could show a splash screen here
  }

  return (
    <NavigationContainer>
      <StatusBar 
        backgroundColor={isDarkMode ? '#1a1a1a' : COLORS.primary} 
        barStyle={isDarkMode ? 'light-content' : 'light-content'}
      />
      
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          <Stack.Screen name="Auth" component={AuthStack} />
        ) : user.isAdmin ? (
          <Stack.Screen name="AdminApp" component={AdminStack} />
        ) : (
          <>
            <Stack.Screen name="MainApp" component={MainDrawer} />
            <Stack.Screen 
              name="DonationForm" 
              component={DonationFormScreen}
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: COLORS.primary },
                headerTintColor: COLORS.surface,
                headerTitleStyle: { fontWeight: 'bold' },
                title: 'نموذج التبرع',
              }}
            />
            <Stack.Screen 
              name="MonetaryDonation" 
              component={MonetaryDonationScreen}
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: COLORS.primary },
                headerTintColor: COLORS.surface,
                headerTitleStyle: { fontWeight: 'bold' },
                title: 'التبرع النقدي',
              }}
            />
            <Stack.Screen 
              name="DonationConfirmation" 
              component={DonationConfirmationScreen}
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: COLORS.primary },
                headerTintColor: COLORS.surface,
                headerTitleStyle: { fontWeight: 'bold' },
                title: 'تأكيد التبرع',
                headerLeft: () => null, // Prevent going back
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
