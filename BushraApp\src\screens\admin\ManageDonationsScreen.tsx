import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import CustomInput from '@/components/common/CustomInput';
import CustomButton from '@/components/common/CustomButton';
import { COLORS } from '@/utils/constants';
import { donationsStorage } from '@/services/storage';
import { 
  getDonationTypeLabel, 
  getDonationStatusLabel, 
  formatDateArabic,
  showConfirmAlert 
} from '@/utils/helpers';
import { RootStackParamList, MaterialDonation, MonetaryDonation } from '@/types';

type ManageDonationsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ManageDonations'>;

const ManageDonationsScreen: React.FC = () => {
  const navigation = useNavigation<ManageDonationsScreenNavigationProp>();
  
  const [donations, setDonations] = useState<(MaterialDonation | MonetaryDonation)[]>([]);
  const [filteredDonations, setFilteredDonations] = useState<(MaterialDonation | MonetaryDonation)[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [selectedDonation, setSelectedDonation] = useState<MaterialDonation | MonetaryDonation | null>(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);

  useEffect(() => {
    loadDonations();
  }, []);

  useEffect(() => {
    filterDonations();
  }, [donations, searchQuery, statusFilter]);

  const loadDonations = async () => {
    try {
      const allDonations = await donationsStorage.getAllDonations();
      setDonations(allDonations);
    } catch (error) {
      console.error('Error loading donations:', error);
      Alert.alert('خطأ', 'حدث خطأ في تحميل التبرعات');
    }
  };

  const filterDonations = () => {
    let filtered = donations;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(donation => donation.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(donation =>
        donation.donorName.toLowerCase().includes(query) ||
        donation.donorPhone.includes(query) ||
        getDonationTypeLabel(donation.type).toLowerCase().includes(query)
      );
    }

    setFilteredDonations(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDonations();
    setRefreshing(false);
  };

  const handleStatusChange = async (donationId: string, newStatus: 'approved' | 'rejected') => {
    const statusText = newStatus === 'approved' ? 'الموافقة على' : 'رفض';
    
    showConfirmAlert(
      `${statusText} التبرع`,
      `هل أنت متأكد من ${statusText} هذا التبرع؟`,
      async () => {
        try {
          await donationsStorage.updateDonationStatus(donationId, newStatus);
          await loadDonations();
          Alert.alert('نجح', `تم ${statusText} التبرع بنجاح`);
        } catch (error) {
          Alert.alert('خطأ', `حدث خطأ في ${statusText} التبرع`);
        }
      }
    );
  };

  const handleDeleteDonation = async (donationId: string) => {
    showConfirmAlert(
      'حذف التبرع',
      'هل أنت متأكد من حذف هذا التبرع؟ لا يمكن التراجع عن هذا الإجراء.',
      async () => {
        try {
          await donationsStorage.deleteDonation(donationId);
          await loadDonations();
          Alert.alert('نجح', 'تم حذف التبرع بنجاح');
        } catch (error) {
          Alert.alert('خطأ', 'حدث خطأ في حذف التبرع');
        }
      }
    );
  };

  const showDonationDetails = (donation: MaterialDonation | MonetaryDonation) => {
    setSelectedDonation(donation);
    setDetailsModalVisible(true);
  };

  const isMonetaryDonation = (donation: MaterialDonation | MonetaryDonation): donation is MonetaryDonation => {
    return 'amount' in donation;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return COLORS.warning;
      case 'approved': return COLORS.success;
      case 'rejected': return COLORS.error;
      default: return COLORS.textSecondary;
    }
  };

  const renderDonationItem = ({ item }: { item: MaterialDonation | MonetaryDonation }) => (
    <View style={styles.donationCard}>
      <TouchableOpacity onPress={() => showDonationDetails(item)}>
        <View style={styles.donationHeader}>
          <View style={styles.donationInfo}>
            <Text style={styles.donorName}>{item.donorName}</Text>
            <Text style={styles.donationType}>
              {getDonationTypeLabel(item.type)}
              {isMonetaryDonation(item) && ` - ${item.amount.toLocaleString()} د.ع`}
            </Text>
            <Text style={styles.donationDate}>
              {formatDateArabic(item.createdAt)}
            </Text>
          </View>
          
          <View style={styles.statusContainer}>
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {getDonationStatusLabel(item.status)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {item.status === 'pending' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.approveButton]}
            onPress={() => handleStatusChange(item.id, 'approved')}
          >
            <Icon name="check" size={16} color={COLORS.surface} />
            <Text style={styles.actionButtonText}>موافقة</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={() => handleStatusChange(item.id, 'rejected')}
          >
            <Icon name="close" size={16} color={COLORS.surface} />
            <Text style={styles.actionButtonText}>رفض</Text>
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteDonation(item.id)}
      >
        <Icon name="delete" size={16} color={COLORS.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>إدارة التبرعات</Text>
        <Text style={styles.headerSubtitle}>
          إجمالي التبرعات: {donations.length}
        </Text>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <CustomInput
          placeholder="البحث بالاسم أو الهاتف أو نوع التبرع"
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
        />

        <View style={styles.statusFilters}>
          {[
            { key: 'all', label: 'الكل' },
            { key: 'pending', label: 'في الانتظار' },
            { key: 'approved', label: 'مقبول' },
            { key: 'rejected', label: 'مرفوض' },
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterButton,
                statusFilter === filter.key && styles.activeFilterButton,
              ]}
              onPress={() => setStatusFilter(filter.key as any)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  statusFilter === filter.key && styles.activeFilterButtonText,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Donations List */}
      <FlatList
        data={filteredDonations}
        renderItem={renderDonationItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Icon name="inbox" size={48} color={COLORS.textSecondary} />
            <Text style={styles.emptyStateText}>لا توجد تبرعات</Text>
          </View>
        }
      />

      {/* Details Modal */}
      <Modal
        visible={detailsModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setDetailsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedDonation && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>تفاصيل التبرع</Text>
                  <TouchableOpacity onPress={() => setDetailsModalVisible(false)}>
                    <Icon name="close" size={24} color={COLORS.text} />
                  </TouchableOpacity>
                </View>

                <View style={styles.modalBody}>
                  <Text style={styles.detailLabel}>الاسم: {selectedDonation.donorName}</Text>
                  <Text style={styles.detailLabel}>الهاتف: {selectedDonation.donorPhone}</Text>
                  <Text style={styles.detailLabel}>النوع: {getDonationTypeLabel(selectedDonation.type)}</Text>
                  
                  {isMonetaryDonation(selectedDonation) && (
                    <Text style={styles.detailLabel}>
                      المبلغ: {selectedDonation.amount.toLocaleString()} د.ع
                    </Text>
                  )}
                  
                  <Text style={styles.detailLabel}>العنوان: {selectedDonation.address}</Text>
                  <Text style={styles.detailLabel}>
                    التاريخ: {formatDateArabic(selectedDonation.createdAt)}
                  </Text>
                  <Text style={styles.detailLabel}>
                    الحالة: {getDonationStatusLabel(selectedDonation.status)}
                  </Text>
                </View>

                <CustomButton
                  title="إغلاق"
                  onPress={() => setDetailsModalVisible(false)}
                  style={styles.closeButton}
                />
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: 20,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.surface,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: COLORS.surface + 'CC',
  },
  filtersContainer: {
    padding: 16,
    backgroundColor: COLORS.surface,
  },
  searchInput: {
    marginBottom: 12,
  },
  statusFilters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterButton: {
    flex: 1,
    padding: 8,
    marginHorizontal: 2,
    backgroundColor: COLORS.background,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeFilterButton: {
    backgroundColor: COLORS.primary,
  },
  filterButtonText: {
    fontSize: 12,
    color: COLORS.text,
    fontWeight: '600',
  },
  activeFilterButtonText: {
    color: COLORS.surface,
  },
  listContainer: {
    padding: 16,
  },
  donationCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    position: 'relative',
  },
  donationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  donationInfo: {
    flex: 1,
  },
  donorName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  donationType: {
    fontSize: 14,
    color: COLORS.primary,
    marginBottom: 4,
  },
  donationDate: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 6,
    gap: 4,
  },
  approveButton: {
    backgroundColor: COLORS.success,
  },
  rejectButton: {
    backgroundColor: COLORS.error,
  },
  actionButtonText: {
    color: COLORS.surface,
    fontSize: 14,
    fontWeight: '600',
  },
  deleteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    padding: 4,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.background,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  modalBody: {
    marginBottom: 20,
  },
  detailLabel: {
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 8,
    textAlign: 'right',
  },
  closeButton: {
    marginTop: 16,
  },
});

export default ManageDonationsScreen;
