import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import DatePicker from 'react-native-date-picker';
import CustomInput from '@/components/common/CustomInput';
import CustomButton from '@/components/common/CustomButton';
import LocationPicker from '@/components/common/LocationPicker';
import { COLORS } from '@/utils/constants';
import { getDonationTypeLabel, generateId, formatDateArabic } from '@/utils/helpers';
import { donationsStorage, userStorage, settingsStorage } from '@/services/storage';
import { RootStackParamList, MonetaryDonation, User } from '@/types';

type MonetaryDonationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MonetaryDonation'>;
type MonetaryDonationScreenRouteProp = RouteProp<RootStackParamList, 'MonetaryDonation'>;

const MonetaryDonationScreen: React.FC = () => {
  const navigation = useNavigation<MonetaryDonationScreenNavigationProp>();
  const route = useRoute<MonetaryDonationScreenRouteProp>();
  const { subType } = route.params;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [zainCashNumber, setZainCashNumber] = useState('');

  const [formData, setFormData] = useState({
    amount: '',
    paymentMethod: 'electronic' as 'electronic' | 'cash',
    donorName: '',
    donorPhone: '',
    address: '',
    location: null as { latitude: number; longitude: number } | null,
    collectionDate: new Date(),
    collectionTime: new Date(),
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadUserData();
    loadSettings();
    showReligiousContent();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await userStorage.getUser();
      setUser(userData);
      if (userData && !userData.isGuest) {
        setFormData(prev => ({
          ...prev,
          donorName: userData.fullName,
          donorPhone: userData.phone,
        }));
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadSettings = async () => {
    try {
      const settings = await settingsStorage.getSettings();
      setZainCashNumber(settings.zainCashNumber);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const showReligiousContent = () => {
    const religiousContent = getReligiousContent(subType);
    Alert.alert(
      getDonationTypeLabel(subType),
      religiousContent,
      [{ text: 'موافق' }]
    );
  };

  const getReligiousContent = (donationType: string): string => {
    const contents = {
      orphan_sponsorship: 'قال رسول الله ﷺ: "أنا وكافل اليتيم في الجنة هكذا" وأشار بالسبابة والوسطى',
      bill_payment: 'قال رسول الله ﷺ: "من نفس عن مؤمن كربة من كرب الدنيا نفس الله عنه كربة من كرب يوم القيامة"',
      zakat: 'قال الله تعالى: "وَأَقِيمُوا الصَّلَاةَ وَآتُوا الزَّكَاةَ"',
      general: 'قال الله تعالى: "وَمَا تُنفِقُوا مِنْ خَيْرٍ فَلِأَنفُسِكُمْ"',
    };
    return contents[donationType as keyof typeof contents] || contents.general;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount.trim()) {
      newErrors.amount = 'المبلغ مطلوب';
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = 'يجب إدخال مبلغ صحيح';
    }

    if (formData.paymentMethod === 'cash') {
      if (!formData.donorName.trim()) {
        newErrors.donorName = 'الاسم الكامل مطلوب';
      }
      if (!formData.donorPhone.trim()) {
        newErrors.donorPhone = 'رقم الهاتف مطلوب';
      }
      if (!formData.address.trim()) {
        newErrors.address = 'العنوان مطلوب';
      }
      if (!formData.location) {
        newErrors.location = 'يجب تحديد الموقع الجغرافي';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    if (formData.paymentMethod === 'electronic') {
      handleElectronicPayment();
    } else {
      handleCashPayment();
    }
  };

  const handleElectronicPayment = () => {
    Alert.alert(
      'الدفع الإلكتروني',
      `يرجى تحويل مبلغ ${formData.amount} دينار عراقي إلى رقم زين كاش: ${zainCashNumber}\n\nبعد التحويل، سيتم تأكيد التبرع تلقائياً.`,
      [
        { text: 'إلغاء', style: 'cancel' },
        { text: 'تم التحويل', onPress: confirmElectronicPayment },
      ]
    );
  };

  const confirmElectronicPayment = async () => {
    setLoading(true);
    try {
      const donation: MonetaryDonation = {
        id: generateId(),
        type: subType as any,
        amount: Number(formData.amount),
        paymentMethod: 'electronic',
        donorName: user?.fullName || 'مجهول',
        donorPhone: user?.phone || '',
        address: 'دفع إلكتروني',
        location: { latitude: 0, longitude: 0 },
        paymentDetails: {
          zainCashNumber: zainCashNumber,
        },
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await donationsStorage.saveDonation(donation);
      navigation.navigate('DonationConfirmation', { donation });
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في تسجيل التبرع');
    } finally {
      setLoading(false);
    }
  };

  const handleCashPayment = async () => {
    setLoading(true);
    try {
      const donation: MonetaryDonation = {
        id: generateId(),
        type: subType as any,
        amount: Number(formData.amount),
        paymentMethod: 'cash',
        donorName: formData.donorName.trim(),
        donorPhone: formData.donorPhone.trim(),
        address: formData.address.trim(),
        location: formData.location!,
        paymentDetails: {
          collectionDate: formData.collectionDate,
          collectionTime: formData.collectionTime.toLocaleTimeString('ar-IQ', {
            hour: '2-digit',
            minute: '2-digit',
          }),
        },
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await donationsStorage.saveDonation(donation);
      navigation.navigate('DonationConfirmation', { donation });
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في تسجيل التبرع');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>{getDonationTypeLabel(subType)}</Text>
        <Text style={styles.subtitle}>ادخل المبلغ واختر طريقة الدفع</Text>
      </View>

      <View style={styles.form}>
        <CustomInput
          label="المبلغ (دينار عراقي) *"
          placeholder="أدخل المبلغ"
          value={formData.amount}
          onChangeText={(value) => updateFormData('amount', value)}
          keyboardType="numeric"
          error={errors.amount}
        />

        <View style={styles.paymentMethodContainer}>
          <Text style={styles.paymentMethodLabel}>طريقة الدفع</Text>
          
          <TouchableOpacity
            style={[
              styles.paymentMethodButton,
              formData.paymentMethod === 'electronic' && styles.selectedPaymentMethod,
            ]}
            onPress={() => updateFormData('paymentMethod', 'electronic')}
          >
            <Text style={[
              styles.paymentMethodText,
              formData.paymentMethod === 'electronic' && styles.selectedPaymentMethodText,
            ]}>
              💳 دفع إلكتروني (زين كاش)
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.paymentMethodButton,
              formData.paymentMethod === 'cash' && styles.selectedPaymentMethod,
            ]}
            onPress={() => updateFormData('paymentMethod', 'cash')}
          >
            <Text style={[
              styles.paymentMethodText,
              formData.paymentMethod === 'cash' && styles.selectedPaymentMethodText,
            ]}>
              💵 دفع نقدي
            </Text>
          </TouchableOpacity>
        </View>

        {formData.paymentMethod === 'cash' && (
          <View style={styles.cashPaymentForm}>
            <Text style={styles.sectionTitle}>بيانات الاستلام النقدي</Text>
            
            <CustomInput
              label="الاسم الثلاثي *"
              placeholder="أدخل اسمك الكامل"
              value={formData.donorName}
              onChangeText={(value) => updateFormData('donorName', value)}
              error={errors.donorName}
            />

            <CustomInput
              label="رقم الهاتف *"
              placeholder="+964xxxxxxxxx"
              value={formData.donorPhone}
              onChangeText={(value) => updateFormData('donorPhone', value)}
              keyboardType="phone-pad"
              error={errors.donorPhone}
            />

            <CustomInput
              label="العنوان *"
              placeholder="أدخل عنوانك الكامل"
              value={formData.address}
              onChangeText={(value) => updateFormData('address', value)}
              multiline
              numberOfLines={2}
              error={errors.address}
            />

            <View style={styles.dateTimeContainer}>
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateTimeLabel}>تاريخ الاستلام</Text>
                <Text style={styles.dateTimeValue}>
                  {formatDateArabic(formData.collectionDate).split(',')[0]}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowTimePicker(true)}
              >
                <Text style={styles.dateTimeLabel}>وقت الاستلام</Text>
                <Text style={styles.dateTimeValue}>
                  {formData.collectionTime.toLocaleTimeString('ar-IQ', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </TouchableOpacity>
            </View>

            <LocationPicker
              location={formData.location}
              onLocationChange={(location) => updateFormData('location', location)}
            />
            {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
          </View>
        )}

        <CustomButton
          title={formData.paymentMethod === 'electronic' ? 'متابعة الدفع الإلكتروني' : 'تأكيد طلب الاستلام'}
          onPress={handleSubmit}
          loading={loading}
          style={styles.submitButton}
        />
      </View>

      <DatePicker
        modal
        open={showDatePicker}
        date={formData.collectionDate}
        mode="date"
        minimumDate={new Date()}
        onConfirm={(date) => {
          setShowDatePicker(false);
          updateFormData('collectionDate', date);
        }}
        onCancel={() => setShowDatePicker(false)}
        title="اختر تاريخ الاستلام"
        confirmText="تأكيد"
        cancelText="إلغاء"
      />

      <DatePicker
        modal
        open={showTimePicker}
        date={formData.collectionTime}
        mode="time"
        onConfirm={(time) => {
          setShowTimePicker(false);
          updateFormData('collectionTime', time);
        }}
        onCancel={() => setShowTimePicker(false)}
        title="اختر وقت الاستلام"
        confirmText="تأكيد"
        cancelText="إلغاء"
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  paymentMethodContainer: {
    marginBottom: 24,
  },
  paymentMethodLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
    textAlign: 'right',
  },
  paymentMethodButton: {
    padding: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: COLORS.background,
    marginBottom: 8,
  },
  selectedPaymentMethod: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  paymentMethodText: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: 'center',
    fontWeight: '600',
  },
  selectedPaymentMethodText: {
    color: COLORS.primary,
  },
  cashPaymentForm: {
    marginTop: 16,
    padding: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
    textAlign: 'right',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  dateTimeButton: {
    flex: 1,
    padding: 16,
    backgroundColor: COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.textSecondary,
    alignItems: 'center',
  },
  dateTimeLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  dateTimeValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
    color: COLORS.error,
    marginTop: -12,
    marginBottom: 16,
    textAlign: 'right',
  },
  submitButton: {
    marginTop: 24,
    marginBottom: 32,
  },
});

export default MonetaryDonationScreen;
