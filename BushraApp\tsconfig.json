{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@assets/*": ["../assets/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}