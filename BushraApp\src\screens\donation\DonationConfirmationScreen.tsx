import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Animated,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import ViewShot from 'react-native-view-shot';
import Icon from 'react-native-vector-icons/MaterialIcons';
import CustomButton from '@/components/common/CustomButton';
import { COLORS, APP_NAME } from '@/utils/constants';
import { 
  getDonationTypeLabel, 
  getDonationStatusLabel, 
  formatDateArabic 
} from '@/utils/helpers';
import { RootStackParamList, MaterialDonation, MonetaryDonation } from '@/types';

type DonationConfirmationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'DonationConfirmation'>;
type DonationConfirmationScreenRouteProp = RouteProp<RootStackParamList, 'DonationConfirmation'>;

const DonationConfirmationScreen: React.FC = () => {
  const navigation = useNavigation<DonationConfirmationScreenNavigationProp>();
  const route = useRoute<DonationConfirmationScreenRouteProp>();
  const { donation } = route.params;
  
  const viewShotRef = useRef<ViewShot>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  React.useEffect(() => {
    // Success animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleDownloadReceipt = async () => {
    try {
      if (viewShotRef.current) {
        const uri = await viewShotRef.current.capture();
        Alert.alert(
          'تم حفظ الإيصال',
          'تم حفظ إيصال التبرع في معرض الصور',
          [{ text: 'موافق' }]
        );
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في حفظ الإيصال');
    }
  };

  const handleBackToHome = () => {
    navigation.navigate('Home');
  };

  const isMonetaryDonation = (donation: MaterialDonation | MonetaryDonation): donation is MonetaryDonation => {
    return 'amount' in donation;
  };

  const generateBarcodeData = () => {
    return `BUSHRA-${donation.id}-${Date.now()}`;
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Success Animation */}
      <Animated.View 
        style={[
          styles.successContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <View style={styles.successIcon}>
          <Icon name="check-circle" size={80} color={COLORS.success} />
        </View>
        <Text style={styles.successTitle}>شكراً لمساهمتك!</Text>
        <Text style={styles.successMessage}>
          سيتم التواصل معك قريباً لتأكيد التبرع
        </Text>
      </Animated.View>

      {/* Receipt */}
      <ViewShot ref={viewShotRef} options={{ format: 'png', quality: 0.9 }}>
        <View style={styles.receiptContainer}>
          {/* Header */}
          <View style={styles.receiptHeader}>
            <View style={styles.logoContainer}>
              <Icon name="volunteer-activism" size={40} color={COLORS.primary} />
            </View>
            <Text style={styles.appTitle}>{APP_NAME}</Text>
            <Text style={styles.receiptTitle}>إيصال تبرع</Text>
          </View>

          {/* Donation Details */}
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>الاسم الكامل:</Text>
              <Text style={styles.detailValue}>{donation.donorName}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>رقم الهاتف:</Text>
              <Text style={styles.detailValue}>{donation.donorPhone}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>التاريخ والوقت:</Text>
              <Text style={styles.detailValue}>
                {formatDateArabic(donation.createdAt)}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>نوع التبرع:</Text>
              <Text style={styles.detailValue}>
                {getDonationTypeLabel(donation.type)}
              </Text>
            </View>

            {isMonetaryDonation(donation) && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>المبلغ:</Text>
                <Text style={styles.detailValue}>
                  {donation.amount.toLocaleString()} دينار عراقي
                </Text>
              </View>
            )}

            {!isMonetaryDonation(donation) && donation.condition && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>حالة التبرع:</Text>
                <Text style={styles.detailValue}>
                  {donation.condition === 'new' ? 'جديد' : 
                   donation.condition === 'used' ? 'مستعمل' : 'مستعمل وجديد'}
                </Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>حالة الطلب:</Text>
              <Text style={[styles.detailValue, styles.statusValue]}>
                {getDonationStatusLabel(donation.status)}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>العنوان:</Text>
              <Text style={styles.detailValue}>{donation.address}</Text>
            </View>

            {donation.location && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>الإحداثيات:</Text>
                <Text style={styles.detailValue}>
                  {donation.location.latitude.toFixed(6)}, {donation.location.longitude.toFixed(6)}
                </Text>
              </View>
            )}
          </View>

          {/* Barcode */}
          <View style={styles.barcodeContainer}>
            <Text style={styles.barcodeText}>{generateBarcodeData()}</Text>
            <View style={styles.barcode}>
              {Array.from({ length: 20 }).map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.barcodeBar,
                    { height: Math.random() * 30 + 20 },
                  ]}
                />
              ))}
            </View>
          </View>

          {/* Footer */}
          <View style={styles.receiptFooter}>
            <Text style={styles.footerText}>
              جزاكم الله خيراً على تبرعكم
            </Text>
            <Text style={styles.footerSubtext}>
              "من صدقاتكم وزكاتكم نُعين أسرة، ونُطعم جائعًا، ونُسعد يتيمًا."
            </Text>
          </View>
        </View>
      </ViewShot>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        <CustomButton
          title="تحميل الإيصال كصورة"
          onPress={handleDownloadReceipt}
          variant="outline"
          style={styles.downloadButton}
        />

        <CustomButton
          title="العودة للرئيسية"
          onPress={handleBackToHome}
          style={styles.homeButton}
        />
      </View>

      {/* Motivational Message */}
      <View style={styles.motivationalContainer}>
        <Text style={styles.motivationalText}>
          بارك الله فيكم وجعل تبرعكم في ميزان حسناتكم
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    padding: 16,
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingVertical: 24,
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.success,
    marginBottom: 8,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  receiptContainer: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  receiptHeader: {
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primary,
    paddingBottom: 16,
    marginBottom: 20,
  },
  logoContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  appTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  receiptTitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.background,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  detailValue: {
    fontSize: 14,
    color: COLORS.text,
    flex: 2,
    textAlign: 'left',
  },
  statusValue: {
    color: COLORS.warning,
    fontWeight: '600',
  },
  barcodeContainer: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.background,
    borderRadius: 8,
  },
  barcodeText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  barcode: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 50,
  },
  barcodeBar: {
    width: 2,
    backgroundColor: COLORS.text,
    marginHorizontal: 1,
  },
  receiptFooter: {
    alignItems: 'center',
    borderTopWidth: 2,
    borderTopColor: COLORS.primary,
    paddingTop: 16,
  },
  footerText: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  footerSubtext: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
  actionsContainer: {
    marginBottom: 24,
  },
  downloadButton: {
    marginBottom: 12,
  },
  homeButton: {
    marginBottom: 12,
  },
  motivationalContainer: {
    backgroundColor: COLORS.primary + '10',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  motivationalText: {
    fontSize: 16,
    color: COLORS.primary,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 24,
  },
});

export default DonationConfirmationScreen;
