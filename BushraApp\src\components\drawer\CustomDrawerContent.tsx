import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  DrawerContentScrollView,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { COLORS } from '@/utils/constants';
import { userStorage, settingsStorage } from '@/services/storage';
import { openWhatsApp, showConfirmAlert } from '@/utils/helpers';
import { User } from '@/types';

interface DrawerItemProps {
  icon: string;
  title: string;
  onPress: () => void;
  showBadge?: boolean;
}

const DrawerItem: React.FC<DrawerItemProps> = ({ icon, title, onPress, showBadge }) => (
  <TouchableOpacity style={styles.drawerItem} onPress={onPress} activeOpacity={0.7}>
    <View style={styles.drawerItemContent}>
      <Icon name={icon} size={24} color={COLORS.primary} />
      <Text style={styles.drawerItemText}>{title}</Text>
      {showBadge && <View style={styles.badge} />}
    </View>
  </TouchableOpacity>
);

const CustomDrawerContent: React.FC<DrawerContentComponentProps> = (props) => {
  const [user, setUser] = useState<User | null>(null);
  const [whatsappNumber, setWhatsappNumber] = useState('');

  useEffect(() => {
    loadUserData();
    loadSettings();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await userStorage.getUser();
      setUser(userData);
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadSettings = async () => {
    try {
      const settings = await settingsStorage.getSettings();
      setWhatsappNumber(settings.organizationInfo.whatsappNumber);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const handleAboutUs = () => {
    Alert.alert(
      'من نحن',
      'مؤسسة بُشرى الخيرية هي منظمة غير ربحية تهدف إلى مساعدة المحتاجين وكفالة الأيتام وتقديم المساعدات العينية والنقدية للأسر المتعففة.\n\nرسالتنا: نسعى لبناء مجتمع متكافل يسوده الخير والعطاء.\n\nرؤيتنا: أن نكون الجسر الذي يربط بين أهل الخير والمحتاجين.',
      [{ text: 'موافق' }]
    );
  };

  const handleContactUs = () => {
    if (whatsappNumber) {
      const message = 'السلام عليكم، أود التواصل معكم بخصوص تطبيق بُشرى';
      openWhatsApp(whatsappNumber, message);
    } else {
      Alert.alert('خطأ', 'رقم الواتساب غير متوفر حالياً');
    }
  };

  const handleTermsAndConditions = () => {
    Alert.alert(
      'الشروط والأحكام',
      '1. يجب أن تكون التبرعات من مصادر حلال\n2. المؤسسة غير مسؤولة عن التبرعات المرفوضة\n3. يحق للمؤسسة رفض أي تبرع لا يتوافق مع سياستها\n4. جميع التبرعات تخضع للمراجعة قبل القبول\n5. المؤسسة تحتفظ بحق تعديل هذه الشروط\n\nسياسة الخصوصية:\n- نحن نحترم خصوصية المتبرعين\n- لا نشارك البيانات الشخصية مع أطراف ثالثة\n- نستخدم البيانات فقط لأغراض التبرع والتواصل',
      [{ text: 'موافق' }]
    );
  };

  const handleBankAccount = async () => {
    try {
      const settings = await settingsStorage.getSettings();
      const { bankAccount, organizationInfo } = settings;
      
      Alert.alert(
        'حساب التبرع',
        `اسم المصرف: ${bankAccount.bankName}\nرقم الحساب: ${bankAccount.accountNumber}\nاسم الحساب: ${bankAccount.accountName}\n\nهاتف المؤسسة: ${organizationInfo.phone}\nالعنوان: ${organizationInfo.address}`,
        [{ text: 'موافق' }]
      );
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في تحميل بيانات الحساب');
    }
  };

  const handleLogin = () => {
    props.navigation.navigate('Login');
  };

  const handleLogout = () => {
    showConfirmAlert(
      'تسجيل الخروج',
      'هل أنت متأكد من تسجيل الخروج؟',
      async () => {
        try {
          await userStorage.removeUser();
          props.navigation.replace('Auth');
        } catch (error) {
          Alert.alert('خطأ', 'حدث خطأ في تسجيل الخروج');
        }
      }
    );
  };

  return (
    <DrawerContentScrollView {...props} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Icon name="volunteer-activism" size={40} color={COLORS.primary} />
        </View>
        <Text style={styles.appName}>بُشرى</Text>
        <Text style={styles.appSubtitle}>منصة الخير والعطاء</Text>
        
        {user && (
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {user.isGuest ? 'ضيف' : user.fullName}
            </Text>
            {!user.isGuest && (
              <Text style={styles.userEmail}>{user.email}</Text>
            )}
          </View>
        )}
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        <DrawerItem
          icon="info"
          title="من نحن"
          onPress={handleAboutUs}
        />
        
        <DrawerItem
          icon="chat"
          title="تواصل معنا"
          onPress={handleContactUs}
        />
        
        <DrawerItem
          icon="description"
          title="الشروط والأحكام"
          onPress={handleTermsAndConditions}
        />
        
        <DrawerItem
          icon="account-balance"
          title="حساب التبرع"
          onPress={handleBankAccount}
        />

        <View style={styles.divider} />

        {user?.isGuest ? (
          <DrawerItem
            icon="login"
            title="تسجيل الدخول / إنشاء حساب"
            onPress={handleLogin}
          />
        ) : (
          <DrawerItem
            icon="logout"
            title="تسجيل الخروج"
            onPress={handleLogout}
          />
        )}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          تطبيق بُشرى v1.0.0
        </Text>
        <Text style={styles.footerSubtext}>
          جميع الحقوق محفوظة © 2024
        </Text>
      </View>
    </DrawerContentScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  header: {
    padding: 20,
    backgroundColor: COLORS.primary + '10',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.background,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  appSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 16,
  },
  userInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  menuContainer: {
    flex: 1,
    paddingTop: 16,
  },
  drawerItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  drawerItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  drawerItemText: {
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 16,
    flex: 1,
  },
  badge: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.error,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.background,
    marginVertical: 8,
    marginHorizontal: 20,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: COLORS.background,
  },
  footerText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 10,
    color: COLORS.textSecondary,
  },
});

export default CustomDrawerContent;
