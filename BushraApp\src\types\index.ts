// User Types
export interface User {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  isGuest?: boolean;
}

// Donation Types
export interface BaseDonation {
  id: string;
  donorName: string;
  donorPhone: string;
  address: string;
  location: {
    latitude: number;
    longitude: number;
  };
  notes?: string;
  images?: string[];
  status: 'pending' | 'approved' | 'collected' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

export interface MaterialDonation extends BaseDonation {
  type: 'food' | 'clothes' | 'furniture' | 'electronics' | 'other';
  condition?: 'new' | 'used' | 'mixed';
}

export interface MonetaryDonation extends BaseDonation {
  type: 'orphan_sponsorship' | 'bill_payment' | 'zakat' | 'general';
  amount: number;
  paymentMethod: 'electronic' | 'cash';
  paymentDetails?: {
    zainCashNumber?: string;
    collectionDate?: Date;
    collectionTime?: string;
  };
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Home: undefined;
  DonationForm: {
    type: 'food' | 'clothes' | 'furniture' | 'electronics' | 'other' | 'monetary';
  };
  MonetaryDonation: {
    subType: 'orphan_sponsorship' | 'bill_payment' | 'zakat' | 'general';
  };
  DonationConfirmation: {
    donation: MaterialDonation | MonetaryDonation;
  };
  AdminPanel: undefined;
};

// Settings Types
export interface AppSettings {
  organizationInfo: {
    name: string;
    description: string;
    address: string;
    phone: string;
    whatsappNumber: string;
  };
  bankAccount: {
    bankName: string;
    accountNumber: string;
    accountName: string;
  };
  zainCashNumber: string;
  termsAndConditions: string;
  dailyVerse: string;
  dailyHadith: string;
}

// Admin Types
export interface AdminStats {
  totalDonations: number;
  sponsoredOrphans: number;
  totalDonors: number;
  pendingRequests: number;
}
