# تطبيق بُشرى - منصة الخير والعطاء

تطبيق React Native لإدارة التبرعات الخيرية باللغة العربية مع دعم RTL كامل.

## الميزات الرئيسية

### للمتبرعين:
- **تسجيل الدخول والتسجيل**: إنشاء حساب أو الاستمرار كضيف
- **التبرع العيني**: تبرع بالطعام، الملابس، الأثاث، الأجهزة الإلكترونية
- **التبرع النقدي**: كفالة الأيتام، دفع الفواتير، الزكاة، التبرعات العامة
- **خيارات الدفع**: زين كاش الإلكتروني أو الاستلام النقدي
- **تحديد الموقع**: خريطة تفاعلية لتحديد موقع التبرع
- **رفع الصور**: إمكانية رفع حتى 5 صور للتبرعات العينية
- **إيصال التبرع**: تحميل إيصال التبرع كصورة مع باركود
- **المحتوى الديني**: آيات وأحاديث يومية تحفيزية

### للإدارة:
- **لوحة التحكم**: إحصائيات شاملة عن التبرعات
- **إدارة التبرعات**: موافقة أو رفض أو حذف التبرعات
- **البحث والفلترة**: البحث في التبرعات حسب الحالة والنوع
- **تصدير البيانات**: تصدير التبرعات والإحصائيات
- **الإعدادات**: إدارة معلومات المؤسسة وأرقام الحسابات

## التقنيات المستخدمة

- **React Native** مع TypeScript
- **React Navigation** للتنقل
- **AsyncStorage** لحفظ البيانات محلياً
- **React Native Maps** للخرائط
- **React Native Image Picker** لرفع الصور
- **React Native Vector Icons** للأيقونات
- **React Native Date Picker** لاختيار التاريخ والوقت
- **React Native View Shot** لحفظ الإيصالات كصور

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- React Native CLI
- Android Studio (للأندرويد)
- Xcode (للـ iOS)

## تعليمات التثبيت والتشغيل

### 1. تثبيت المتطلبات:
```bash
npm install -g react-native-cli
```

### 2. تثبيت المكتبات:
```bash
cd BushraApp
npm install
```

### 3. تشغيل التطبيق على الأندرويد:
```bash
npx react-native run-android
```

### 4. تشغيل التطبيق على iOS:
```bash
cd ios && pod install && cd ..
npx react-native run-ios
```

## بيانات تسجيل الدخول للإدارة

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Managerbushra

## هيكل المشروع

```
BushraApp/
├── src/
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   │   ├── common/         # المكونات العامة
│   │   └── drawer/         # مكونات القائمة الجانبية
│   ├── screens/            # شاشات التطبيق
│   │   ├── auth/          # شاشات المصادقة
│   │   ├── main/          # الشاشات الرئيسية
│   │   ├── donation/      # شاشات التبرع
│   │   └── admin/         # شاشات الإدارة
│   ├── navigation/         # إعدادات التنقل
│   ├── services/          # خدمات التطبيق
│   ├── types/             # تعريفات TypeScript
│   └── utils/             # الوظائف المساعدة والثوابت
├── android/               # ملفات الأندرويد
├── ios/                   # ملفات iOS
└── package.json          # إعدادات المشروع
```

## الميزات المتقدمة

### الوضع الليلي
- دعم كامل للوضع الليلي/النهاري
- حفظ تفضيلات المستخدم

### المحتوى الديني اليومي
- آيات قرآنية وأحاديث نبوية تتغير يومياً
- محتوى تحفيزي للتبرع

### الخرائط والموقع
- تحديد الموقع الجغرافي بدقة
- عرض الموقع على الخريطة
- طلب أذونات الموقع تلقائياً

### إدارة الصور
- رفع الصور من الكاميرا أو المعرض
- ضغط الصور تلقائياً
- عرض معاينة للصور المرفوعة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا عبر:
- الواتساب: +964 770 123 4567
- البريد الإلكتروني: <EMAIL>

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**جزاكم الله خيراً على مساهمتكم في العمل الخيري** 🤲
