import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchImageLibrary, launchCamera, ImagePickerResponse } from 'react-native-image-picker';
import { COLORS, VALIDATION } from '@/utils/constants';
import { requestCameraPermission } from '@/utils/helpers';

interface ImagePickerProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  label?: string;
}

const CustomImagePicker: React.FC<ImagePickerProps> = ({
  images,
  onImagesChange,
  maxImages = VALIDATION.MAX_IMAGES,
  label = 'الصور (اختياري)',
}) => {
  const showImagePicker = () => {
    if (images.length >= maxImages) {
      Alert.alert('تنبيه', `يمكنك رفع ${maxImages} صور كحد أقصى`);
      return;
    }

    Alert.alert(
      'اختيار الصورة',
      'كيف تريد إضافة الصورة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        { text: 'من المعرض', onPress: openGallery },
        { text: 'التقاط صورة', onPress: openCamera },
      ]
    );
  };

  const openGallery = () => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchImageLibrary(options, handleImageResponse);
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('خطأ', 'يجب السماح بالوصول للكاميرا لالتقاط الصور');
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchCamera(options, handleImageResponse);
  };

  const handleImageResponse = (response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      return;
    }

    if (response.assets && response.assets[0]) {
      const asset = response.assets[0];
      
      // Check file size
      if (asset.fileSize && asset.fileSize > VALIDATION.MAX_IMAGE_SIZE) {
        Alert.alert('خطأ', 'حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت');
        return;
      }

      if (asset.uri) {
        const newImages = [...images, asset.uri];
        onImagesChange(newImages);
      }
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesContainer}>
        {images.map((uri, index) => (
          <View key={index} style={styles.imageWrapper}>
            <Image source={{ uri }} style={styles.image} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeImage(index)}
            >
              <Icon name="close" size={16} color={COLORS.surface} />
            </TouchableOpacity>
          </View>
        ))}
        
        {images.length < maxImages && (
          <TouchableOpacity style={styles.addButton} onPress={showImagePicker}>
            <Icon name="add-a-photo" size={32} color={COLORS.primary} />
            <Text style={styles.addButtonText}>إضافة صورة</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      
      <Text style={styles.helperText}>
        يمكنك رفع حتى {maxImages} صور (حجم كل صورة أقل من 5 ميجابايت)
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
    textAlign: 'right',
  },
  imagesContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: 12,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: COLORS.background,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: COLORS.error,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface,
  },
  addButtonText: {
    fontSize: 12,
    color: COLORS.primary,
    marginTop: 4,
    textAlign: 'center',
  },
  helperText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
});

export default CustomImagePicker;
