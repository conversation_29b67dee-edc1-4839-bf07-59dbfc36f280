import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';
import Geolocation from '@react-native-community/geolocation';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { COLORS } from '@/utils/constants';
import { requestLocationPermission } from '@/utils/helpers';

interface LocationPickerProps {
  location: {
    latitude: number;
    longitude: number;
  } | null;
  onLocationChange: (location: { latitude: number; longitude: number }) => void;
  label?: string;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  location,
  onLocationChange,
  label = 'الموقع الجغرافي',
}) => {
  const [region, setRegion] = useState<Region>({
    latitude: 33.3152,
    longitude: 44.3661,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (location) {
      setRegion({
        ...region,
        latitude: location.latitude,
        longitude: location.longitude,
      });
    }
  }, [location]);

  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      Alert.alert('خطأ', 'يجب السماح بالوصول للموقع لتحديد موقعك الحالي');
      return;
    }

    setLoading(true);
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const newLocation = { latitude, longitude };
        
        onLocationChange(newLocation);
        setRegion({
          ...region,
          latitude,
          longitude,
        });
        setLoading(false);
      },
      (error) => {
        console.error('Location error:', error);
        Alert.alert('خطأ', 'حدث خطأ في تحديد الموقع الحالي');
        setLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      }
    );
  };

  const onMapPress = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    onLocationChange({ latitude, longitude });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          region={region}
          onRegionChangeComplete={setRegion}
          onPress={onMapPress}
          showsUserLocation
          showsMyLocationButton={false}
        >
          {location && (
            <Marker
              coordinate={location}
              title="موقع التبرع"
              description="اضغط واسحب لتغيير الموقع"
              draggable
              onDragEnd={(event) => {
                const { latitude, longitude } = event.nativeEvent.coordinate;
                onLocationChange({ latitude, longitude });
              }}
            />
          )}
        </MapView>
        
        <TouchableOpacity
          style={styles.currentLocationButton}
          onPress={getCurrentLocation}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={COLORS.surface} />
          ) : (
            <Icon name="my-location" size={24} color={COLORS.surface} />
          )}
        </TouchableOpacity>
      </View>
      
      <TouchableOpacity style={styles.selectLocationButton} onPress={getCurrentLocation}>
        <Icon name="location-on" size={20} color={COLORS.primary} />
        <Text style={styles.selectLocationText}>تحديد الموقع الحالي</Text>
      </TouchableOpacity>
      
      {location && (
        <View style={styles.coordinatesContainer}>
          <Text style={styles.coordinatesText}>
            خط العرض: {location.latitude.toFixed(6)}
          </Text>
          <Text style={styles.coordinatesText}>
            خط الطول: {location.longitude.toFixed(6)}
          </Text>
        </View>
      )}
      
      <Text style={styles.helperText}>
        اضغط على الخريطة لتحديد الموقع أو استخدم زر "تحديد الموقع الحالي"
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
    textAlign: 'right',
  },
  mapContainer: {
    position: 'relative',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 12,
  },
  map: {
    flex: 1,
  },
  currentLocationButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  selectLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginBottom: 8,
  },
  selectLocationText: {
    fontSize: 16,
    color: COLORS.primary,
    marginLeft: 8,
    fontWeight: '600',
  },
  coordinatesContainer: {
    backgroundColor: COLORS.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  coordinatesText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'right',
    marginBottom: 2,
  },
  helperText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'right',
    lineHeight: 16,
  },
});

export default LocationPicker;
