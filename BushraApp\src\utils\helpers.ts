import { Al<PERSON>, Linking, PermissionsAndroid, Platform } from 'react-native';
import { VALIDATION } from './constants';

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Format phone number
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Add +964 prefix if not present
  if (!cleaned.startsWith('964')) {
    return `+964${cleaned}`;
  }
  
  return `+${cleaned}`;
};

// Validate email
export const validateEmail = (email: string): boolean => {
  return VALIDATION.EMAIL_REGEX.test(email);
};

// Validate phone number
export const validatePhone = (phone: string): boolean => {
  const formatted = formatPhoneNumber(phone);
  return VALIDATION.PHONE_REGEX.test(formatted);
};

// Validate password
export const validatePassword = (password: string): boolean => {
  return password.length >= VALIDATION.MIN_PASSWORD_LENGTH;
};

// Format date to Arabic
export const formatDateArabic = (date: Date): string => {
  return new Intl.DateTimeFormat('ar-IQ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// Open WhatsApp
export const openWhatsApp = (phoneNumber: string, message?: string): void => {
  const url = `whatsapp://send?phone=${phoneNumber}${message ? `&text=${encodeURIComponent(message)}` : ''}`;
  
  Linking.canOpenURL(url)
    .then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        Alert.alert('خطأ', 'تطبيق واتساب غير مثبت على جهازك');
      }
    })
    .catch(() => {
      Alert.alert('خطأ', 'حدث خطأ في فتح واتساب');
    });
};

// Request location permission
export const requestLocationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'إذن الموقع',
          message: 'يحتاج التطبيق إلى إذن الوصول للموقع لتحديد عنوانك',
          buttonNeutral: 'اسأل لاحقاً',
          buttonNegative: 'إلغاء',
          buttonPositive: 'موافق',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  }
  return true;
};

// Request camera permission
export const requestCameraPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: 'إذن الكاميرا',
          message: 'يحتاج التطبيق إلى إذن الوصول للكاميرا لالتقاط الصور',
          buttonNeutral: 'اسأل لاحقاً',
          buttonNegative: 'إلغاء',
          buttonPositive: 'موافق',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  }
  return true;
};

// Show alert
export const showAlert = (title: string, message: string, onPress?: () => void): void => {
  Alert.alert(title, message, [{ text: 'موافق', onPress }]);
};

// Show confirmation alert
export const showConfirmAlert = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
): void => {
  Alert.alert(
    title,
    message,
    [
      { text: 'إلغاء', style: 'cancel', onPress: onCancel },
      { text: 'موافق', onPress: onConfirm },
    ]
  );
};

// Truncate text
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Get donation type label in Arabic
export const getDonationTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    food: 'غذاء',
    clothes: 'ملابس',
    furniture: 'أثاث',
    electronics: 'أجهزة',
    other: 'أخرى',
    orphan_sponsorship: 'كفالة أيتام',
    bill_payment: 'سداد فواتير',
    zakat: 'زكاة',
    general: 'تبرع عام',
  };
  return labels[type] || type;
};

// Get donation status label in Arabic
export const getDonationStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    pending: 'قيد المراجعة',
    approved: 'مقبول',
    collected: 'تم الاستلام',
    rejected: 'مرفوض',
  };
  return labels[status] || status;
};
