import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import DonationCard from '@/components/common/DonationCard';
import { COLORS, DONATION_TYPES, MONETARY_TYPES } from '@/utils/constants';
import { settingsStorage } from '@/services/storage';
import { RootStackParamList } from '@/types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [dailyVerse, setDailyVerse] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDailyContent();
  }, []);

  const loadDailyContent = async () => {
    try {
      const settings = await settingsStorage.getSettings();
      setDailyVerse(settings.dailyVerse);
    } catch (error) {
      console.error('Error loading daily content:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDailyContent();
    setRefreshing(false);
  };

  const handleDonationPress = (type: string) => {
    if (type === DONATION_TYPES.MONETARY) {
      // Navigate to monetary donation options
      navigation.navigate('MonetaryDonation', { subType: MONETARY_TYPES.GENERAL });
    } else {
      navigation.navigate('DonationForm', { type: type as any });
    }
  };

  const handleMonetarySubTypePress = (subType: string) => {
    navigation.navigate('MonetaryDonation', { subType: subType as any });
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.welcomeText}>مرحباً بك في</Text>
        <Text style={styles.appName}>تطبيق بُشرى</Text>
        <Text style={styles.subtitle}>منصة الخير والعطاء</Text>
      </View>

      {/* Daily Verse */}
      {dailyVerse && (
        <View style={styles.verseContainer}>
          <Text style={styles.verseText}>{dailyVerse}</Text>
        </View>
      )}

      {/* Material Donations Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>التبرع العيني</Text>
        <View style={styles.cardsContainer}>
          <View style={styles.cardRow}>
            <DonationCard
              title="التبرع بالغذاء"
              icon="restaurant"
              onPress={() => handleDonationPress(DONATION_TYPES.FOOD)}
            />
            <DonationCard
              title="التبرع بالملابس"
              icon="checkroom"
              onPress={() => handleDonationPress(DONATION_TYPES.CLOTHES)}
            />
          </View>
          <View style={styles.cardRow}>
            <DonationCard
              title="التبرع بالأثاث"
              icon="chair"
              onPress={() => handleDonationPress(DONATION_TYPES.FURNITURE)}
            />
            <DonationCard
              title="التبرع بالأجهزة"
              icon="devices"
              onPress={() => handleDonationPress(DONATION_TYPES.ELECTRONICS)}
            />
          </View>
          <View style={styles.cardRow}>
            <DonationCard
              title="التبرع بأخرى"
              icon="category"
              onPress={() => handleDonationPress(DONATION_TYPES.OTHER)}
              style={styles.fullWidthCard}
            />
          </View>
        </View>
      </View>

      {/* Monetary Donations Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>التبرع النقدي</Text>
        <View style={styles.cardsContainer}>
          <View style={styles.cardRow}>
            <DonationCard
              title="كفالة أيتام"
              icon="child-care"
              onPress={() => handleMonetarySubTypePress(MONETARY_TYPES.ORPHAN_SPONSORSHIP)}
              backgroundColor={COLORS.accent + '20'}
            />
            <DonationCard
              title="سداد فواتير"
              icon="receipt"
              onPress={() => handleMonetarySubTypePress(MONETARY_TYPES.BILL_PAYMENT)}
              backgroundColor={COLORS.info + '20'}
            />
          </View>
          <View style={styles.cardRow}>
            <DonationCard
              title="زكاة"
              icon="volunteer-activism"
              onPress={() => handleMonetarySubTypePress(MONETARY_TYPES.ZAKAT)}
              backgroundColor={COLORS.success + '20'}
            />
            <DonationCard
              title="تبرع عام"
              icon="favorite"
              onPress={() => handleMonetarySubTypePress(MONETARY_TYPES.GENERAL)}
              backgroundColor={COLORS.warning + '20'}
            />
          </View>
        </View>
      </View>

      {/* Motivational Footer */}
      <View style={styles.motivationalContainer}>
        <Text style={styles.motivationalText}>
          "من صدقاتكم وزكاتكم نُعين أسرة، ونُطعم جائعًا، ونُسعد يتيمًا."
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 20,
  },
  welcomeText: {
    fontSize: 18,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  verseContainer: {
    backgroundColor: COLORS.surface,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primary,
  },
  verseText: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
    textAlign: 'right',
  },
  cardsContainer: {
    flex: 1,
  },
  cardRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  fullWidthCard: {
    flex: 2,
    marginHorizontal: 8,
  },
  motivationalContainer: {
    backgroundColor: COLORS.primary + '10',
    padding: 20,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 32,
  },
  motivationalText: {
    fontSize: 16,
    color: COLORS.primary,
    textAlign: 'center',
    fontWeight: '600',
    lineHeight: 24,
  },
});

export default HomeScreen;
