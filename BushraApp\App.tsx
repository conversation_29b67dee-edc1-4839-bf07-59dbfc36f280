import React, { useEffect } from 'react';
import { LogBox, PermissionsAndroid, Platform } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { settingsStorage } from './src/services/storage';

// Ignore specific warnings for development
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'VirtualizedLists should never be nested',
]);

const App: React.FC = () => {
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Request necessary permissions
      await requestPermissions();
      
      // Initialize default settings if first time
      await initializeDefaultSettings();
      
      console.log('App initialized successfully');
    } catch (error) {
      console.error('Error initializing app:', error);
    }
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        ];

        const granted = await PermissionsAndroid.requestMultiple(permissions);
        
        console.log('Permissions granted:', granted);
      } catch (error) {
        console.error('Error requesting permissions:', error);
      }
    }
  };

  const initializeDefaultSettings = async () => {
    try {
      // Check if settings already exist
      const existingSettings = await settingsStorage.getSettings();
      
      if (!existingSettings.organizationInfo.name) {
        // Initialize with default settings
        const defaultSettings = {
          isDarkMode: false,
          organizationInfo: {
            name: 'مؤسسة بُشرى الخيرية',
            phone: '+964 ************',
            email: '<EMAIL>',
            address: 'بغداد، العراق',
            whatsappNumber: '+964 ************',
          },
          bankAccount: {
            bankName: 'مصرف الرافدين',
            accountNumber: '************',
            accountName: 'مؤسسة بُشرى الخيرية',
          },
          zainCashNumber: '***********',
          dailyContent: {
            verse: 'وَمَا تُنفِقُوا مِنْ خَيْرٍ فَلِأَنفُسِكُمْ',
            hadith: 'قال رسول الله ﷺ: "الصدقة تطفئ الخطيئة كما يطفئ الماء النار"',
          },
          lastContentUpdate: new Date().toDateString(),
        };

        await settingsStorage.saveSettings(defaultSettings);
        console.log('Default settings initialized');
      }
    } catch (error) {
      console.error('Error initializing default settings:', error);
    }
  };

  return <AppNavigator />;
};

export default App;
