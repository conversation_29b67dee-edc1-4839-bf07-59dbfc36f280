import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { COLORS } from '@/utils/constants';
import { donationsStorage, settingsStorage } from '@/services/storage';
import { getDonationTypeLabel, getDonationStatusLabel, formatDateArabic } from '@/utils/helpers';
import { RootStackParamList, MaterialDonation, MonetaryDonation, AdminStats } from '@/types';

type AdminDashboardScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AdminDashboard'>;

interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color }) => (
  <View style={[styles.statCard, { borderLeftColor: color }]}>
    <View style={styles.statContent}>
      <View style={styles.statTextContainer}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
        <Icon name={icon} size={24} color={color} />
      </View>
    </View>
  </View>
);

const AdminDashboardScreen: React.FC = () => {
  const navigation = useNavigation<AdminDashboardScreenNavigationProp>();
  
  const [stats, setStats] = useState<AdminStats>({
    totalDonations: 0,
    pendingDonations: 0,
    approvedDonations: 0,
    rejectedDonations: 0,
    totalAmount: 0,
    materialDonations: 0,
    monetaryDonations: 0,
  });
  
  const [recentDonations, setRecentDonations] = useState<(MaterialDonation | MonetaryDonation)[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const donations = await donationsStorage.getAllDonations();
      calculateStats(donations);
      setRecentDonations(donations.slice(0, 5)); // Show last 5 donations
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('خطأ', 'حدث خطأ في تحميل بيانات لوحة التحكم');
    }
  };

  const calculateStats = (donations: (MaterialDonation | MonetaryDonation)[]) => {
    const newStats: AdminStats = {
      totalDonations: donations.length,
      pendingDonations: donations.filter(d => d.status === 'pending').length,
      approvedDonations: donations.filter(d => d.status === 'approved').length,
      rejectedDonations: donations.filter(d => d.status === 'rejected').length,
      totalAmount: donations
        .filter((d): d is MonetaryDonation => 'amount' in d)
        .reduce((sum, d) => sum + d.amount, 0),
      materialDonations: donations.filter(d => !('amount' in d)).length,
      monetaryDonations: donations.filter(d => 'amount' in d).length,
    };
    
    setStats(newStats);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleManageDonations = () => {
    navigation.navigate('ManageDonations');
  };

  const handleSettings = () => {
    navigation.navigate('AdminSettings');
  };

  const handleNotifications = () => {
    navigation.navigate('AdminNotifications');
  };

  const handleExportData = () => {
    Alert.alert(
      'تصدير البيانات',
      'اختر نوع البيانات المراد تصديرها',
      [
        { text: 'إلغاء', style: 'cancel' },
        { text: 'تصدير التبرعات', onPress: exportDonations },
        { text: 'تصدير الإحصائيات', onPress: exportStats },
      ]
    );
  };

  const exportDonations = async () => {
    try {
      const donations = await donationsStorage.getAllDonations();
      // In a real app, this would generate and save a CSV/Excel file
      Alert.alert('نجح التصدير', `تم تصدير ${donations.length} تبرع بنجاح`);
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في تصدير البيانات');
    }
  };

  const exportStats = () => {
    // In a real app, this would generate a statistics report
    Alert.alert('نجح التصدير', 'تم تصدير الإحصائيات بنجاح');
  };

  const isMonetaryDonation = (donation: MaterialDonation | MonetaryDonation): donation is MonetaryDonation => {
    return 'amount' in donation;
  };

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>لوحة التحكم الإدارية</Text>
        <Text style={styles.headerSubtitle}>مرحباً بك في نظام إدارة التبرعات</Text>
      </View>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <StatCard
          title="إجمالي التبرعات"
          value={stats.totalDonations}
          icon="volunteer-activism"
          color={COLORS.primary}
        />
        
        <StatCard
          title="في الانتظار"
          value={stats.pendingDonations}
          icon="pending"
          color={COLORS.warning}
        />
        
        <StatCard
          title="تم الموافقة"
          value={stats.approvedDonations}
          icon="check-circle"
          color={COLORS.success}
        />
        
        <StatCard
          title="تم الرفض"
          value={stats.rejectedDonations}
          icon="cancel"
          color={COLORS.error}
        />
        
        <StatCard
          title="إجمالي المبلغ"
          value={`${stats.totalAmount.toLocaleString()} د.ع`}
          icon="attach-money"
          color={COLORS.primary}
        />
        
        <StatCard
          title="تبرعات عينية"
          value={stats.materialDonations}
          icon="inventory"
          color="#FF9800"
        />
        
        <StatCard
          title="تبرعات نقدية"
          value={stats.monetaryDonations}
          icon="payments"
          color="#4CAF50"
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>الإجراءات السريعة</Text>
        
        <View style={styles.actionsGrid}>
          <TouchableOpacity style={styles.actionButton} onPress={handleManageDonations}>
            <Icon name="list-alt" size={32} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>إدارة التبرعات</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleNotifications}>
            <Icon name="notifications" size={32} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>الإشعارات</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleSettings}>
            <Icon name="settings" size={32} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>الإعدادات</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleExportData}>
            <Icon name="file-download" size={32} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>تصدير البيانات</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recent Donations */}
      <View style={styles.recentContainer}>
        <Text style={styles.sectionTitle}>أحدث التبرعات</Text>
        
        {recentDonations.length === 0 ? (
          <View style={styles.emptyState}>
            <Icon name="inbox" size={48} color={COLORS.textSecondary} />
            <Text style={styles.emptyStateText}>لا توجد تبرعات حالياً</Text>
          </View>
        ) : (
          recentDonations.map((donation) => (
            <View key={donation.id} style={styles.donationCard}>
              <View style={styles.donationHeader}>
                <Text style={styles.donationName}>{donation.donorName}</Text>
                <Text style={[
                  styles.donationStatus,
                  { color: donation.status === 'pending' ? COLORS.warning : 
                           donation.status === 'approved' ? COLORS.success : COLORS.error }
                ]}>
                  {getDonationStatusLabel(donation.status)}
                </Text>
              </View>
              
              <Text style={styles.donationType}>
                {getDonationTypeLabel(donation.type)}
                {isMonetaryDonation(donation) && ` - ${donation.amount.toLocaleString()} د.ع`}
              </Text>
              
              <Text style={styles.donationDate}>
                {formatDateArabic(donation.createdAt)}
              </Text>
              
              <Text style={styles.donationPhone}>{donation.donorPhone}</Text>
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: 20,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.surface,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: COLORS.surface + 'CC',
  },
  statsContainer: {
    padding: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statTextContainer: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
    textAlign: 'right',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionButtonText: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '600',
  },
  recentContainer: {
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginTop: 12,
  },
  donationCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  donationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  donationName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
  },
  donationStatus: {
    fontSize: 14,
    fontWeight: '600',
  },
  donationType: {
    fontSize: 14,
    color: COLORS.primary,
    marginBottom: 4,
  },
  donationDate: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  donationPhone: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
});

export default AdminDashboardScreen;
