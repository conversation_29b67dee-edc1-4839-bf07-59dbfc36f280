import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import CustomInput from '@/components/common/CustomInput';
import CustomButton from '@/components/common/CustomButton';
import { COLORS, ADMIN_EMAIL, ADMIN_PASSWORD } from '@/utils/constants';
import { validateEmail } from '@/utils/helpers';
import { userStorage } from '@/services/storage';
import { RootStackParamList, User } from '@/types';
import { generateId } from '@/utils/helpers';

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const validateForm = (): boolean => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!validateEmail(email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Check if admin credentials
      if (email === ADMIN_EMAIL && password === ADMIN_PASSWORD) {
        const adminUser: User = {
          id: 'admin',
          fullName: 'المدير',
          email: ADMIN_EMAIL,
          phone: '',
        };
        await userStorage.saveUser(adminUser);
        navigation.replace('AdminPanel');
      } else {
        // For demo purposes, accept any valid email/password combination
        // In a real app, this would authenticate with a backend
        const user: User = {
          id: generateId(),
          fullName: 'مستخدم',
          email: email,
          phone: '',
        };
        await userStorage.saveUser(user);
        navigation.replace('Main');
      }
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'نسيت كلمة المرور؟',
      'يرجى التواصل مع الإدارة لاستعادة كلمة المرور',
      [{ text: 'موافق' }]
    );
  };

  const handleContinueAsGuest = async () => {
    try {
      const guestUser: User = {
        id: 'guest',
        fullName: 'ضيف',
        email: '',
        phone: '',
        isGuest: true,
      };
      await userStorage.saveUser(guestUser);
      navigation.replace('Main');
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في الدخول كضيف');
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>تسجيل الدخول</Text>
        <Text style={styles.subtitle}>مرحباً بك في تطبيق بُشرى</Text>
      </View>

      <View style={styles.form}>
        <CustomInput
          label="البريد الإلكتروني"
          placeholder="أدخل بريدك الإلكتروني"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          leftIcon="email"
          error={errors.email}
        />

        <CustomInput
          label="كلمة المرور"
          placeholder="أدخل كلمة المرور"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          leftIcon="lock"
          error={errors.password}
        />

        <TouchableOpacity onPress={handleForgotPassword} style={styles.forgotPassword}>
          <Text style={styles.forgotPasswordText}>نسيت كلمة المرور؟</Text>
        </TouchableOpacity>

        <CustomButton
          title="تسجيل الدخول"
          onPress={handleLogin}
          loading={loading}
          style={styles.loginButton}
        />

        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>أو</Text>
          <View style={styles.dividerLine} />
        </View>

        <CustomButton
          title="الاستمرار كضيف"
          onPress={handleContinueAsGuest}
          variant="outline"
          style={styles.guestButton}
        />

        <View style={styles.footer}>
          <Text style={styles.footerText}>ليس لديك حساب؟ </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={styles.registerLink}>إنشاء حساب جديد</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.motivationalContainer}>
        <Text style={styles.motivationalText}>
          "من صدقاتكم وزكاتكم نُعين أسرة، ونُطعم جائعًا، ونُسعد يتيمًا."
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: COLORS.primary,
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 20,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.textSecondary,
  },
  dividerText: {
    marginHorizontal: 16,
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  guestButton: {
    marginBottom: 30,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  registerLink: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  motivationalContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primary,
  },
  motivationalText: {
    fontSize: 14,
    color: COLORS.text,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
});

export default LoginScreen;
