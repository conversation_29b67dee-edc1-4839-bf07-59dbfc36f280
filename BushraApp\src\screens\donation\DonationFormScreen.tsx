import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Picker } from '@react-native-picker/picker';
import DatePicker from 'react-native-date-picker';
import CustomInput from '@/components/common/CustomInput';
import CustomButton from '@/components/common/CustomButton';
import LocationPicker from '@/components/common/LocationPicker';
import ImagePicker from '@/components/common/ImagePicker';
import { COLORS, ITEM_CONDITIONS } from '@/utils/constants';
import { getDonationTypeLabel, generateId, formatDateArabic } from '@/utils/helpers';
import { donationsStorage, userStorage } from '@/services/storage';
import { RootStackParamList, MaterialDonation, User } from '@/types';

type DonationFormScreenNavigationProp = StackNavigationProp<RootStackParamList, 'DonationForm'>;
type DonationFormScreenRouteProp = RouteProp<RootStackParamList, 'DonationForm'>;

const DonationFormScreen: React.FC = () => {
  const navigation = useNavigation<DonationFormScreenNavigationProp>();
  const route = useRoute<DonationFormScreenRouteProp>();
  const { type } = route.params;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const [formData, setFormData] = useState({
    donorName: '',
    donorPhone: '',
    date: new Date(),
    time: new Date(),
    address: '',
    location: null as { latitude: number; longitude: number } | null,
    notes: '',
    condition: ITEM_CONDITIONS.USED,
    images: [] as string[],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadUserData();
    showTermsAndConditions();
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await userStorage.getUser();
      setUser(userData);
      if (userData && !userData.isGuest) {
        setFormData(prev => ({
          ...prev,
          donorName: userData.fullName,
          donorPhone: userData.phone,
        }));
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const showTermsAndConditions = () => {
    const typeLabel = getDonationTypeLabel(type);
    const religiousContent = getReligiousContent(type);
    
    Alert.alert(
      `شروط التبرع - ${typeLabel}`,
      `${religiousContent}\n\nالشروط:\n1. يجب أن تكون التبرعات نظيفة وصالحة للاستخدام\n2. لا نقبل المواد المنتهية الصلاحية أو التالفة\n3. يحق للمؤسسة فحص التبرعات قبل القبول\n4. سيتم التواصل معك لتحديد موعد الاستلام\n5. التبرع يجب أن يكون من مصدر حلال`,
      [
        { text: 'لا أوافق', style: 'cancel', onPress: () => navigation.goBack() },
        { text: 'أوافق على الشروط', onPress: () => setAgreedToTerms(true) },
      ]
    );
  };

  const getReligiousContent = (donationType: string): string => {
    const contents = {
      food: 'قال رسول الله ﷺ: "من أطعم مؤمناً على جوع أطعمه الله من ثمار الجنة"',
      clothes: 'قال رسول الله ﷺ: "من كسا مؤمناً ثوباً على عري كساه الله من خضر الجنة"',
      furniture: 'قال الله تعالى: "وَمَا تُنفِقُوا مِنْ خَيْرٍ فَلِأَنفُسِكُمْ"',
      electronics: 'قال رسول الله ﷺ: "الصدقة تطفئ الخطيئة كما يطفئ الماء النار"',
      other: 'قال الله تعالى: "مَّن ذَا الَّذِي يُقْرِضُ اللَّهَ قَرْضًا حَسَنًا"',
    };
    return contents[donationType as keyof typeof contents] || contents.other;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.donorName.trim()) {
      newErrors.donorName = 'الاسم الكامل مطلوب';
    }

    if (!formData.donorPhone.trim()) {
      newErrors.donorPhone = 'رقم الهاتف مطلوب';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'العنوان مطلوب';
    }

    if (!formData.location) {
      newErrors.location = 'يجب تحديد الموقع الجغرافي';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const donation: MaterialDonation = {
        id: generateId(),
        type: type as any,
        donorName: formData.donorName.trim(),
        donorPhone: formData.donorPhone.trim(),
        address: formData.address.trim(),
        location: formData.location!,
        notes: formData.notes.trim(),
        condition: formData.condition as any,
        images: formData.images,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await donationsStorage.saveDonation(donation);
      
      navigation.navigate('DonationConfirmation', { donation });
    } catch (error) {
      Alert.alert('خطأ', 'حدث خطأ في إرسال طلب التبرع');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!agreedToTerms) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>يرجى الموافقة على الشروط للمتابعة</Text>
      </View>
    );
  }

  const showConditionPicker = ['clothes', 'furniture', 'electronics'].includes(type);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>نموذج {getDonationTypeLabel(type)}</Text>
        <Text style={styles.subtitle}>يرجى ملء جميع البيانات المطلوبة</Text>
      </View>

      <View style={styles.form}>
        <CustomInput
          label="الاسم الكامل *"
          placeholder="أدخل اسمك الكامل"
          value={formData.donorName}
          onChangeText={(value) => updateFormData('donorName', value)}
          error={errors.donorName}
        />

        <CustomInput
          label="رقم الهاتف *"
          placeholder="+964xxxxxxxxx"
          value={formData.donorPhone}
          onChangeText={(value) => updateFormData('donorPhone', value)}
          keyboardType="phone-pad"
          error={errors.donorPhone}
        />

        <View style={styles.dateTimeContainer}>
          <TouchableOpacity
            style={styles.dateTimeButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateTimeLabel}>التاريخ</Text>
            <Text style={styles.dateTimeValue}>
              {formatDateArabic(formData.date).split(',')[0]}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.dateTimeButton}
            onPress={() => setShowTimePicker(true)}
          >
            <Text style={styles.dateTimeLabel}>الوقت</Text>
            <Text style={styles.dateTimeValue}>
              {formData.time.toLocaleTimeString('ar-IQ', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </Text>
          </TouchableOpacity>
        </View>

        <CustomInput
          label="العنوان بالتفصيل *"
          placeholder="أدخل عنوانك الكامل"
          value={formData.address}
          onChangeText={(value) => updateFormData('address', value)}
          multiline
          numberOfLines={3}
          error={errors.address}
        />

        <LocationPicker
          location={formData.location}
          onLocationChange={(location) => updateFormData('location', location)}
        />
        {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}

        {showConditionPicker && (
          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>حالة التبرع</Text>
            <View style={styles.pickerWrapper}>
              <Picker
                selectedValue={formData.condition}
                onValueChange={(value) => updateFormData('condition', value)}
                style={styles.picker}
              >
                <Picker.Item label="مستعمل" value={ITEM_CONDITIONS.USED} />
                <Picker.Item label="جديد" value={ITEM_CONDITIONS.NEW} />
                <Picker.Item label="مستعمل وجديد" value={ITEM_CONDITIONS.MIXED} />
              </Picker>
            </View>
          </View>
        )}

        <CustomInput
          label="ملاحظات (اختياري)"
          placeholder="أي ملاحظات إضافية"
          value={formData.notes}
          onChangeText={(value) => updateFormData('notes', value)}
          multiline
          numberOfLines={3}
        />

        <ImagePicker
          images={formData.images}
          onImagesChange={(images) => updateFormData('images', images)}
        />

        <CustomButton
          title="إرسال طلب التبرع"
          onPress={handleSubmit}
          loading={loading}
          style={styles.submitButton}
        />
      </View>

      <DatePicker
        modal
        open={showDatePicker}
        date={formData.date}
        mode="date"
        onConfirm={(date) => {
          setShowDatePicker(false);
          updateFormData('date', date);
        }}
        onCancel={() => setShowDatePicker(false)}
        title="اختر التاريخ"
        confirmText="تأكيد"
        cancelText="إلغاء"
      />

      <DatePicker
        modal
        open={showTimePicker}
        date={formData.time}
        mode="time"
        onConfirm={(time) => {
          setShowTimePicker(false);
          updateFormData('time', time);
        }}
        onCancel={() => setShowTimePicker(false)}
        title="اختر الوقت"
        confirmText="تأكيد"
        cancelText="إلغاء"
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  dateTimeButton: {
    flex: 1,
    padding: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.textSecondary,
    alignItems: 'center',
  },
  dateTimeLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  dateTimeValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
    textAlign: 'right',
  },
  pickerWrapper: {
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.textSecondary,
  },
  picker: {
    height: 50,
  },
  errorText: {
    fontSize: 14,
    color: COLORS.error,
    marginTop: -12,
    marginBottom: 16,
    textAlign: 'right',
  },
  submitButton: {
    marginTop: 24,
    marginBottom: 32,
  },
});

export default DonationFormScreen;
